
import { auth } from "@/auth";
import { AppSidebar } from "@/components/sidebar/sidebar"
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar"
import { SessionProvider } from "next-auth/react";

export default async function Layout({ children }: { children: React.ReactNode }) {
  const session = await auth();
  return (
    <SessionProvider session={session}>
      <SidebarProvider>
        <AppSidebar session={session!} />
        <main className="flex flex-col w-full h-screen">
          <SidebarTrigger className="m-5" />
          <div className="flex flex-col w-full h-screen items-center justify-center">
            {children}
          </div>
        </main>
      </SidebarProvider>
    </SessionProvider>
  )
}