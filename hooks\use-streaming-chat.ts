"use client";

import { useState, useCallback, useRef } from 'react';
import { useChatContext } from '@/contexts/ChatContext';
import { Message } from '@/types';
import { SSEClient } from '@/lib/sse-client';

interface StreamingResponse {
  type: 'metadata' | 'chunk' | 'complete' | 'error';
  content?: string;
  fullContent?: string;
  chatId?: string;
  chatTitle?: string;
  isNewChat?: boolean;
  message?: string;
  timestamp: string;
}

interface UseStreamingChatOptions {
  onStreamStart?: (chatId: string) => void;
  onStreamChunk?: (content: string) => void;
  onStreamComplete?: (fullContent: string) => void;
  onStreamError?: (error: string) => void;
}

export function useStreamingChat(options: UseStreamingChatOptions = {}) {
  const { addMessage, updateMessage, setCurrentChat, addChat } = useChatContext();
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const sseClientRef = useRef<SSEClient | null>(null);
  const currentStreamContent = useRef<string>('');

  const sendMessage = useCallback(async (
    message: string,
    chatId?: string,
    userEmail?: string
  ): Promise<{ success: boolean; chatId?: string; error?: string }> => {
    if (isStreaming) {
      return { success: false, error: 'Already streaming a message' };
    }

    if (!userEmail) {
      return { success: false, error: 'User email is required' };
    }

    setIsStreaming(true);
    currentStreamContent.current = '';

    try {
      // Add user message optimistically
      const userMessageId = `user-${Date.now()}`;
      const userMessage: Message = {
        id: userMessageId,
        content: message,
        role: "user",
        sender: { id: userEmail, role: "USER", email: userEmail },
        createdAt: new Date()
      };

      // Create placeholder for AI message
      const aiMessageId = `ai-${Date.now()}`;
      const aiMessage: Message = {
        id: aiMessageId,
        content: '',
        role: "assistant",
        sender: { id: "AI", role: "ADMIN" },
        createdAt: new Date()
      };

      setStreamingMessageId(aiMessageId);

      let currentChatId = chatId;

      // For existing chats, add messages immediately
      if (chatId) {
        addMessage(chatId, userMessage);
        addMessage(chatId, aiMessage);
      }

      return new Promise((resolve) => {
        // Create SSE client
        sseClientRef.current = new SSEClient('/api/chat/stream', {
          onMessage: (sseMessage) => {
            const data = sseMessage.data as StreamingResponse;

            switch (data.type) {
              case 'metadata':
                if (data.chatId) {
                  currentChatId = data.chatId;
                  setCurrentChat(currentChatId);

                  // If this is a new chat, add it to the chat list
                  if (data.isNewChat && data.chatTitle) {
                    const newChat = {
                      id: data.chatId,
                      title: data.chatTitle,
                      type: 'owned' as const,
                      userId: userEmail,
                      createdAt: new Date(),
                      shares: [],
                      user: {
                        id: userEmail,
                        name: userEmail,
                        email: userEmail,
                        role: 'USER' as const
                      },
                      sharedWith: []
                    };
                    addChat(newChat);
                  }

                  // Add messages to the new chat if it was just created
                  if (!chatId) {
                    addMessage(currentChatId, userMessage);
                    addMessage(currentChatId, aiMessage);
                  }

                  options.onStreamStart?.(currentChatId);
                }
                break;

              case 'chunk':
                if (data.content && currentChatId) {
                  currentStreamContent.current += data.content;
                  updateMessage(currentChatId, aiMessageId, currentStreamContent.current);
                  options.onStreamChunk?.(data.content);
                }
                break;

              case 'complete':
                if (data.fullContent && currentChatId) {
                  updateMessage(currentChatId, aiMessageId, data.fullContent);
                  options.onStreamComplete?.(data.fullContent);
                }
                setIsStreaming(false);
                setStreamingMessageId(null);
                resolve({ success: true, chatId: currentChatId });
                break;

              case 'error':
                throw new Error(data.message || 'Streaming error occurred');
            }
          },

          onError: (error) => {
            setIsStreaming(false);
            setStreamingMessageId(null);

            if (currentChatId && aiMessageId) {
              updateMessage(currentChatId, aiMessageId, 'Sorry, an error occurred while generating the response.');
            }

            options.onStreamError?.(error.message);
            resolve({ success: false, error: error.message });
          },

          onClose: () => {
            setIsStreaming(false);
            setStreamingMessageId(null);
          }
        });

        // Start the connection
        sseClientRef.current.connect({ message, chatId });
      });

    } catch (error) {
      console.error('Streaming error:', error);

      setIsStreaming(false);
      setStreamingMessageId(null);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      options.onStreamError?.(errorMessage);

      return { success: false, error: errorMessage };
    }
  }, [isStreaming, addMessage, updateMessage, setCurrentChat, addChat, options]);

  const stopStreaming = useCallback(() => {
    if (sseClientRef.current) {
      sseClientRef.current.disconnect();
      sseClientRef.current = null;
    }
    setIsStreaming(false);
    setStreamingMessageId(null);
    currentStreamContent.current = '';
  }, []);

  const isMessageStreaming = useCallback((messageId: string) => {
    return streamingMessageId === messageId;
  }, [streamingMessageId]);

  return {
    sendMessage,
    stopStreaming,
    isStreaming,
    streamingMessageId,
    isMessageStreaming,
  };
}
