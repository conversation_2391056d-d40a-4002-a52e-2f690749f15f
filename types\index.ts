export type Message = {
    id: string;
    content: string;
    role: "user" | "assistant";
    sender?: {
      id: string;
      role: "USER" | "ADMIN";
      name?: string;
      email?: string;
    };
  };

  
// Type definitions
export interface User {
  id: string;
  name: string;
  email: string;
  image?: string | null;
  role: 'ADMIN' | 'USER';
}

export interface Share {
  id: string;
  recipientId: string;
  createdAt: Date;
}

export interface Chat {
  id: string;
  title: string;
  userId: string;
  createdAt: Date;
  shares: Share[];
  user: User;
}

export interface SharedChat extends Chat {
  type: 'shared';
  sharedBy: User;
  sharedAt: Date;
}

export interface OwnedChat extends Chat {
  type: 'owned';
  sharedWith: string[];
}

export type CombinedChat = SharedChat | OwnedChat;