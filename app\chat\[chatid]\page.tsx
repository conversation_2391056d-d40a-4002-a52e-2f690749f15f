"use client";
import { AIInputWithLoading } from "@/components/ui/ai-input-with-loading";
import { MessageContainer } from "@/components/ui/message-container";
import React, { useState, useEffect, useRef } from "react";
import { useSession } from "@/hooks/use-session";
import { useChatContext } from "@/contexts/ChatContext";
import { useStreamingChat } from "@/hooks/use-streaming-chat";
import { Message } from "@/types";

function ChatPage({ params }: { params: { chatid: string } }) {
  const { session } = useSession();
  const { state, loadChatMessages, setCurrentChat } = useChatContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const initialized = useRef(false);

  // Set current chat and load messages
  useEffect(() => {
    if (!initialized.current && session?.user?.email && params.chatid) {
      initialized.current = true;
      setCurrentChat(params.chatid);
      loadChatMessages(params.chatid);
    }
  }, [session, params.chatid, setCurrentChat, loadChatMessages]);

  const { sendMessage, isStreaming, streamingMessageId } = useStreamingChat({
    onStreamStart: (chatId) => {
      console.log('Stream started for chat:', chatId);
    },
    onStreamChunk: (content) => {
      // Content is automatically handled by the chat context
      console.log('Received chunk:', content);
    },
    onStreamComplete: (fullContent) => {
      console.log('Stream completed:', fullContent);
      setIsSubmitting(false);
    },
    onStreamError: (error) => {
      console.error('Streaming error:', error);
      setIsSubmitting(false);
    }
  });

  const handleMessageSubmit = async (message: string) => {
    if (!session?.user?.email) return;

    if (isStreaming || isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      const result = await sendMessage(message, params.chatid, session.user.email);

      if (!result.success) {
        console.error("Message submission failed:", result.error);
        setIsSubmitting(false);
      }
      // Success handling is done in the streaming callbacks
    } catch (error) {
      console.error("Message submission failed:", error);
      setIsSubmitting(false);
    }
  };

  const messages = state.messages[params.chatid] || [];
  const isLoading = state.loadingChats.has(params.chatid);

  return (
    <div className="flex flex-col h-full">
      {messages.length === 0 && !isSubmitting && !isStreaming && isLoading && (
        <div className="flex flex-col items-center space-y-4 justify-center flex-1">
          <h1 className="text-[2rem]">Loading...</h1>
        </div>
      )}

      {messages.length > 0 && (
        <MessageContainer
          messages={messages}
          streamingMessageId={streamingMessageId}
          showTimestamps={true}
          className="flex-1 min-h-0"
        />
      )}

      <div className="flex-shrink-0 p-4 border-t bg-background/80 backdrop-blur-sm">
        <div className="max-w-3xl mx-auto">
          <AIInputWithLoading
            onSubmit={handleMessageSubmit}
            loadingDuration={3000}
            placeholder="Type a message..."
            disabled={isSubmitting || isStreaming}
          />
        </div>
      </div>
    </div>
  );
}

export default ChatPage;